<?php

declare(strict_types=1);

/**
 * Currency Converter Application - PHP 7.4+ Compatible Core Engine
 * 
 * Advanced currency conversion system with intelligent caching,
 * robust error handling, and expert-level PHP 7.4+ compatibility.
 * 
 * PHP Version 7.4+
 * 
 * @category  Finance
 * @package   CurrencyConverter
 * <AUTHOR> Converter Team
 * @license   MIT License
 * @version   2.0.0
 * @since     1.0.0
 */

require_once 'config.php';

/**
 * Currency Conversion Result Data Transfer Object
 * 
 * Immutable value object representing the result of a currency conversion.
 * PHP 7.4+ compatible implementation with expert-level documentation.
 */
final class ConversionResult
{
    private float $originalAmount;
    private string $fromCurrency;
    private string $toCurrency;
    private float $convertedAmount;
    private float $exchangeRate;
    private int $timestamp;
    private bool $fromCache;

    /**
     * Create a new conversion result
     * 
     * @param float $originalAmount The original amount to convert
     * @param string $fromCurrency Source currency code
     * @param string $toCurrency Target currency code
     * @param float $convertedAmount The converted amount
     * @param float $exchangeRate The exchange rate used
     * @param int $timestamp Unix timestamp of conversion
     * @param bool $fromCache Whether the rate was retrieved from cache
     */
    public function __construct(
        float $originalAmount,
        string $fromCurrency,
        string $toCurrency,
        float $convertedAmount,
        float $exchangeRate,
        int $timestamp,
        bool $fromCache = false
    ) {
        $this->originalAmount = $originalAmount;
        $this->fromCurrency = $fromCurrency;
        $this->toCurrency = $toCurrency;
        $this->convertedAmount = $convertedAmount;
        $this->exchangeRate = $exchangeRate;
        $this->timestamp = $timestamp;
        $this->fromCache = $fromCache;
    }

    /**
     * Get original amount
     * 
     * @return float Original amount
     */
    public function getOriginalAmount(): float
    {
        return $this->originalAmount;
    }

    /**
     * Get from currency
     * 
     * @return string From currency code
     */
    public function getFromCurrency(): string
    {
        return $this->fromCurrency;
    }

    /**
     * Get to currency
     * 
     * @return string To currency code
     */
    public function getToCurrency(): string
    {
        return $this->toCurrency;
    }

    /**
     * Get converted amount
     * 
     * @return float Converted amount
     */
    public function getConvertedAmount(): float
    {
        return $this->convertedAmount;
    }

    /**
     * Get exchange rate
     * 
     * @return float Exchange rate
     */
    public function getExchangeRate(): float
    {
        return $this->exchangeRate;
    }

    /**
     * Get timestamp
     * 
     * @return int Unix timestamp
     */
    public function getTimestamp(): int
    {
        return $this->timestamp;
    }

    /**
     * Check if result is from cache
     * 
     * @return bool True if from cache
     */
    public function isFromCache(): bool
    {
        return $this->fromCache;
    }

    /**
     * Convert to array for JSON serialization
     * 
     * @return array<string, mixed> Array representation
     */
    public function toArray(): array
    {
        return [
            'amount' => $this->originalAmount,
            'from_currency' => $this->fromCurrency,
            'to_currency' => $this->toCurrency,
            'converted_amount' => round($this->convertedAmount, AppConfig::DECIMAL_PRECISION),
            'exchange_rate' => $this->exchangeRate,
            'timestamp' => $this->timestamp,
            'from_cache' => $this->fromCache,
            'formatted_amount' => $this->getFormattedAmount(),
            'formatted_result' => $this->getFormattedResult(),
        ];
    }

    /**
     * Get formatted original amount with currency symbol
     * 
     * @return string Formatted amount string
     */
    public function getFormattedAmount(): string
    {
        return SupportedCurrency::getSymbol($this->fromCurrency) . number_format(
            $this->originalAmount,
            AppConfig::DECIMAL_PRECISION
        );
    }

    /**
     * Get formatted converted amount with currency symbol
     * 
     * @return string Formatted result string
     */
    public function getFormattedResult(): string
    {
        return SupportedCurrency::getSymbol($this->toCurrency) . number_format(
            $this->convertedAmount,
            AppConfig::DECIMAL_PRECISION
        );
    }
}

/**
 * Custom Exception for Currency Converter Operations
 * 
 * Provides specific exception handling for currency conversion errors
 * with enhanced error context and debugging information.
 */
final class CurrencyConverterException extends Exception
{
    private array $context;

    /**
     * Create a new currency converter exception
     * 
     * @param string $message Error message
     * @param int $code Error code (default: 0)
     * @param Throwable|null $previous Previous exception for chaining
     * @param array<string, mixed> $context Additional error context
     */
    public function __construct(
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * Get additional error context
     * 
     * @return array<string, mixed> Error context data
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Get detailed error information for logging
     * 
     * @return array<string, mixed> Detailed error information
     */
    public function getDetailedInfo(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->context,
            'trace' => $this->getTraceAsString(),
        ];
    }
}

/**
 * Main Currency Converter Service
 * 
 * High-level service class that orchestrates currency conversion operations
 * using PHP 7.4+ features with expert-level code quality.
 */
final class CurrencyConverter
{
    private string $apiUrl;
    private string $cacheFile;
    private int $cacheDuration;

    /**
     * Create a new currency converter instance
     */
    public function __construct()
    {
        $this->apiUrl = AppConfig::EXCHANGE_API_URL;
        $this->cacheFile = AppConfig::CACHE_FILE;
        $this->cacheDuration = AppConfig::CACHE_DURATION;
    }

    /**
     * Convert currency amount with comprehensive validation and error handling
     * 
     * @param float $amount Amount to convert
     * @param string $fromCurrencyCode Source currency code
     * @param string $toCurrencyCode Target currency code
     * @return ConversionResult Conversion result with all details
     * @throws CurrencyConverterException If conversion fails
     */
    public function convert(
        float $amount,
        string $fromCurrencyCode,
        string $toCurrencyCode
    ): ConversionResult {
        // Validate inputs
        $this->validateAmount($amount);
        $this->validateCurrency($fromCurrencyCode);
        $this->validateCurrency($toCurrencyCode);

        // Handle same currency conversion
        if ($fromCurrencyCode === $toCurrencyCode) {
            return new ConversionResult(
                $amount,
                $fromCurrencyCode,
                $toCurrencyCode,
                $amount,
                1.0,
                time(),
                false
            );
        }

        // Get exchange rates
        $ratesData = $this->getExchangeRates($fromCurrencyCode);
        $rates = $ratesData['rates'];
        $fromCache = $ratesData['from_cache'];

        // Validate that target currency rate exists
        if (!isset($rates[$toCurrencyCode])) {
            throw new CurrencyConverterException(
                ErrorMessage::INVALID_CURRENCY,
                0,
                null,
                [
                    'currency' => $toCurrencyCode,
                    'available_currencies' => array_keys($rates),
                ]
            );
        }

        $exchangeRate = (float)$rates[$toCurrencyCode];
        $convertedAmount = $amount * $exchangeRate;

        return new ConversionResult(
            $amount,
            $fromCurrencyCode,
            $toCurrencyCode,
            $convertedAmount,
            $exchangeRate,
            time(),
            $fromCache
        );
    }

    /**
     * Get all supported currencies
     * 
     * @return array<string, string> Array of currency codes and names
     */
    public function getSupportedCurrencies(): array
    {
        return SupportedCurrency::getAllCurrencies();
    }

    /**
     * Format currency amount with proper symbol and formatting
     * 
     * @param float $amount Amount to format
     * @param string $currencyCode Currency code
     * @return string Formatted currency string
     * @throws CurrencyConverterException If currency is not supported
     */
    public function formatCurrency(float $amount, string $currencyCode): string
    {
        $this->validateCurrency($currencyCode);
        
        return SupportedCurrency::getSymbol($currencyCode) . number_format(
            $amount,
            AppConfig::DECIMAL_PRECISION,
            '.',
            ','
        );
    }

    /**
     * Clear the exchange rate cache
     */
    public function clearCache(): void
    {
        if (file_exists($this->cacheFile)) {
            unlink($this->cacheFile);
        }
    }

    /**
     * Get exchange rates from cache or API
     * 
     * @param string $baseCurrency Base currency for rates
     * @return array{rates: array<string, float>, from_cache: bool} Rates and cache status
     * @throws CurrencyConverterException If rates cannot be retrieved
     */
    private function getExchangeRates(string $baseCurrency): array
    {
        // Try to get from cache first
        $cachedRates = $this->getCachedRates($baseCurrency);
        if ($cachedRates !== null) {
            return [
                'rates' => $cachedRates,
                'from_cache' => true,
            ];
        }

        // Fetch from API
        try {
            $rates = $this->fetchFromApi($baseCurrency);
            
            // Cache the fresh data
            $this->cacheRates($baseCurrency, $rates);
            
            return [
                'rates' => $rates,
                'from_cache' => false,
            ];
        } catch (CurrencyConverterException $e) {
            // If API fails, try to use expired cache as fallback
            $expiredRates = $this->getExpiredCacheRates($baseCurrency);
            if ($expiredRates !== null) {
                error_log("Using expired cache due to API failure: " . $e->getMessage());
                return [
                    'rates' => $expiredRates,
                    'from_cache' => true,
                ];
            }
            
            throw $e;
        }
    }

    /**
     * Get cached exchange rates
     * 
     * @param string $baseCurrency Base currency code
     * @return array<string, float>|null Cached rates or null
     */
    private function getCachedRates(string $baseCurrency): ?array
    {
        if (!file_exists($this->cacheFile)) {
            return null;
        }

        $cacheContent = file_get_contents($this->cacheFile);
        if ($cacheContent === false) {
            return null;
        }

        $cacheData = json_decode($cacheContent, true);
        
        if (!$this->isValidCacheData($cacheData, $baseCurrency)) {
            return null;
        }

        return $cacheData['rates'];
    }

    /**
     * Fetch rates from API
     * 
     * @param string $baseCurrency Base currency code
     * @return array<string, float> Exchange rates
     * @throws CurrencyConverterException If API request fails
     */
    private function fetchFromApi(string $baseCurrency): array
    {
        $url = $this->apiUrl . $baseCurrency;
        $response = $this->makeApiRequest($url);
        
        if ($response === false) {
            throw new CurrencyConverterException(ErrorMessage::API_ERROR);
        }

        $data = json_decode($response, true);
        
        if (!$data || !isset($data['rates']) || !is_array($data['rates'])) {
            throw new CurrencyConverterException(ErrorMessage::API_ERROR);
        }

        return $data['rates'];
    }

    /**
     * Make API request with proper error handling
     * 
     * @param string $url URL to request
     * @return string|false Response body or false on failure
     */
    private function makeApiRequest(string $url)
    {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => AppConfig::API_TIMEOUT,
                'user_agent' => AppConfig::USER_AGENT,
                'header' => [
                    'Accept: application/json',
                    'Cache-Control: no-cache',
                ],
                'ignore_errors' => true,
            ],
        ]);

        return @file_get_contents($url, false, $context);
    }

    /**
     * Cache exchange rates
     * 
     * @param string $baseCurrency Base currency code
     * @param array<string, float> $rates Exchange rates
     */
    private function cacheRates(string $baseCurrency, array $rates): void
    {
        $cacheData = [
            'timestamp' => time(),
            'base' => $baseCurrency,
            'rates' => $rates,
            'version' => '2.0',
        ];

        file_put_contents($this->cacheFile, json_encode($cacheData, JSON_PRETTY_PRINT));
    }

    /**
     * Get expired cache rates as fallback
     * 
     * @param string $baseCurrency Base currency
     * @return array<string, float>|null Expired rates or null if not available
     */
    private function getExpiredCacheRates(string $baseCurrency): ?array
    {
        if (!file_exists($this->cacheFile)) {
            return null;
        }

        $cacheContent = file_get_contents($this->cacheFile);
        if ($cacheContent === false) {
            return null;
        }

        $cacheData = json_decode($cacheContent, true);
        
        if (is_array($cacheData) && 
            isset($cacheData['base'], $cacheData['rates']) &&
            $cacheData['base'] === $baseCurrency &&
            is_array($cacheData['rates'])) {
            return $cacheData['rates'];
        }

        return null;
    }

    /**
     * Validate cache data structure and expiration
     * 
     * @param mixed $cacheData Decoded cache data
     * @param string $baseCurrency Expected base currency
     * @return bool True if cache data is valid and not expired
     */
    private function isValidCacheData($cacheData, string $baseCurrency): bool
    {
        if (!is_array($cacheData)) {
            return false;
        }

        $requiredKeys = ['timestamp', 'base', 'rates'];
        foreach ($requiredKeys as $key) {
            if (!isset($cacheData[$key])) {
                return false;
            }
        }

        // Check if cache is expired
        if ((time() - $cacheData['timestamp']) >= $this->cacheDuration) {
            return false;
        }

        // Check if base currency matches
        if ($cacheData['base'] !== $baseCurrency) {
            return false;
        }

        return is_array($cacheData['rates']);
    }

    /**
     * Validate currency code
     * 
     * @param string $currencyCode Currency code to validate
     * @throws CurrencyConverterException If currency is not supported
     */
    private function validateCurrency(string $currencyCode): void
    {
        if (!SupportedCurrency::isSupported($currencyCode)) {
            throw new CurrencyConverterException(
                ErrorMessage::INVALID_CURRENCY,
                0,
                null,
                [
                    'provided_currency' => $currencyCode,
                    'supported_currencies' => SupportedCurrency::getAllCodes(),
                ]
            );
        }
    }

    /**
     * Validate conversion amount
     * 
     * @param float $amount Amount to validate
     * @throws CurrencyConverterException If amount is invalid
     */
    private function validateAmount(float $amount): void
    {
        if (!is_finite($amount) || $amount < AppConfig::MIN_AMOUNT || $amount > AppConfig::MAX_AMOUNT) {
            throw new CurrencyConverterException(
                ErrorMessage::format(ErrorMessage::INVALID_AMOUNT, AppConfig::MIN_AMOUNT, AppConfig::MAX_AMOUNT),
                0,
                null,
                [
                    'provided_amount' => $amount,
                    'min_amount' => AppConfig::MIN_AMOUNT,
                    'max_amount' => AppConfig::MAX_AMOUNT,
                ]
            );
        }
    }
}
