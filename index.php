<?php

declare(strict_types=1);

/**
 * Currency Converter Application - Main Interface
 *
 * Modern PHP 8.4 compatible web interface for currency conversion
 * with enhanced security, error handling, and user experience.
 *
 * PHP Version 8.4+
 *
 * @category  Web Interface
 * @package   CurrencyConverter
 * <AUTHOR> Converter Team
 * @license   MIT License
 * @version   2.0.0
 * @since     1.0.0
 */

require_once 'CurrencyConverter.php';

/**
 * Application Request Handler
 *
 * Handles HTTP requests with proper validation, security checks,
 * and error handling for the currency converter interface.
 */
final class CurrencyConverterController
{
    private readonly CurrencyConverter $converter;

    /**
     * Initialize the controller
     */
    public function __construct()
    {
        $this->converter = new CurrencyConverter();
    }

    /**
     * Handle incoming HTTP requests
     *
     * @return void
     */
    public function handleRequest(): void
    {
        // Security headers
        $this->setSecurityHeaders();

        // Handle AJAX conversion requests
        if ($this->isAjaxRequest()) {
            $this->handleAjaxRequest();
            return;
        }

        // Render the main interface
        $this->renderInterface();
    }

    /**
     * Set security headers for the response
     *
     * @return void
     */
    private function setSecurityHeaders(): void
    {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }

    /**
     * Check if the current request is an AJAX request
     *
     * @return bool True if AJAX request, false otherwise
     */
    private function isAjaxRequest(): bool
    {
        return $_SERVER['REQUEST_METHOD'] === 'POST' &&
               !empty($_POST) &&
               (isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest' ||
                isset($_POST['ajax']) && $_POST['ajax'] === '1');
    }

    /**
     * Handle AJAX conversion requests
     *
     * @return void
     */
    private function handleAjaxRequest(): void
    {
        header('Content-Type: application/json; charset=utf-8');

        try {
            // Validate request size
            $this->validateRequestSize();

            // Extract and validate input parameters
            $amount = $this->validateAmount($_POST['amount'] ?? '');
            $fromCurrency = $this->sanitizeInput($_POST['from_currency'] ?? AppConfig::DEFAULT_FROM_CURRENCY);
            $toCurrency = $this->sanitizeInput($_POST['to_currency'] ?? AppConfig::DEFAULT_TO_CURRENCY);

            // Perform conversion
            $result = $this->converter->convert($amount, $fromCurrency, $toCurrency);

            // Return successful response
            echo json_encode([
                'success' => true,
                'result' => $result->toArray(),
                'timestamp' => time(),
            ], JSON_THROW_ON_ERROR);

        } catch (CurrencyConverterException $e) {
            // Log detailed error information
            error_log('Currency conversion error: ' . json_encode($e->getDetailedInfo()));

            echo json_encode([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => time(),
            ], JSON_THROW_ON_ERROR);

        } catch (JsonException $e) {
            error_log('JSON encoding error: ' . $e->getMessage());

            echo json_encode([
                'success' => false,
                'error' => 'Internal server error occurred.',
                'timestamp' => time(),
            ]);

        } catch (Throwable $e) {
            // Log unexpected errors
            error_log('Unexpected error in currency converter: ' . $e->getMessage());

            echo json_encode([
                'success' => false,
                'error' => 'An unexpected error occurred. Please try again.',
                'timestamp' => time(),
            ]);
        }

        exit;
    }

    /**
     * Validate request size for security
     *
     * @throws CurrencyConverterException If request is too large
     */
    private function validateRequestSize(): void
    {
        $contentLength = $_SERVER['CONTENT_LENGTH'] ?? 0;

        if ($contentLength > AppConfig::MAX_REQUEST_SIZE) {
            throw new CurrencyConverterException(
                ErrorMessage::SECURITY_ERROR->value,
                context: ['content_length' => $contentLength]
            );
        }
    }

    /**
     * Validate and parse amount input
     *
     * @param string $amountInput Raw amount input
     * @return float Validated amount
     * @throws CurrencyConverterException If amount is invalid
     */
    private function validateAmount(string $amountInput): float
    {
        $sanitized = $this->sanitizeInput($amountInput);

        if ($sanitized === '') {
            throw new CurrencyConverterException(
                ErrorMessage::INVALID_AMOUNT->format(AppConfig::MIN_AMOUNT, AppConfig::MAX_AMOUNT)
            );
        }

        $amount = filter_var($sanitized, FILTER_VALIDATE_FLOAT);

        if ($amount === false) {
            throw new CurrencyConverterException(
                ErrorMessage::INVALID_AMOUNT->format(AppConfig::MIN_AMOUNT, AppConfig::MAX_AMOUNT)
            );
        }

        return $amount;
    }

    /**
     * Sanitize user input
     *
     * @param string $input Raw input
     * @return string Sanitized input
     */
    private function sanitizeInput(string $input): string
    {
        return trim(strip_tags($input));
    }

    /**
     * Render the main application interface
     *
     * @return void
     */
    private function renderInterface(): void
    {
        $currencies = $this->converter->getSupportedCurrencies();
        $defaultFromCurrency = AppConfig::DEFAULT_FROM_CURRENCY;
        $defaultToCurrency = AppConfig::DEFAULT_TO_CURRENCY;
        $defaultAmount = AppConfig::DEFAULT_AMOUNT;

        // Include the HTML template
        include $this->getTemplatePath();
    }

    /**
     * Get the path to the HTML template
     *
     * @return string Template file path
     */
    private function getTemplatePath(): string
    {
        // For now, render inline template
        $this->renderInlineTemplate();
        return '';
    }

    /**
     * Render the HTML template inline
     *
     * @return void
     */
    private function renderInlineTemplate(): void
    {
        $currencies = $this->converter->getSupportedCurrencies();
        $defaultFromCurrency = AppConfig::DEFAULT_FROM_CURRENCY;
        $defaultToCurrency = AppConfig::DEFAULT_TO_CURRENCY;
        $defaultAmount = AppConfig::DEFAULT_AMOUNT;

        ?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Converter v2.0</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💱</text></svg>">
    <meta name="description" content="Professional currency converter with real-time exchange rates">
    <meta name="keywords" content="currency converter, exchange rates, money conversion">
</head>
<body>
    <div class="container">
        <h1>💱 Currency Converter v2.0</h1>

        <form id="converterForm" class="converter-form">
            <input type="hidden" name="ajax" value="1">

            <div class="input-group">
                <label for="amount">Amount</label>
                <input
                    type="number"
                    id="amount"
                    name="amount"
                    step="0.01"
                    min="<?php echo AppConfig::MIN_AMOUNT; ?>"
                    max="<?php echo AppConfig::MAX_AMOUNT; ?>"
                    value="<?php echo htmlspecialchars((string)$defaultAmount, ENT_QUOTES, 'UTF-8'); ?>"
                    placeholder="Enter amount"
                    required
                    autocomplete="off"
                >
            </div>

            <div class="currency-row">
                <div class="currency-group">
                    <label for="fromCurrency">From</label>
                    <select id="fromCurrency" name="from_currency" required>
                        <?php foreach ($currencies as $code => $name): ?>
                            <option value="<?php echo htmlspecialchars($code, ENT_QUOTES, 'UTF-8'); ?>"
                                <?php echo $code === $defaultFromCurrency ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($code . ' - ' . $name, ENT_QUOTES, 'UTF-8'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="button" id="swapBtn" class="swap-btn" title="Swap currencies" aria-label="Swap currencies">
                    ⇄
                </button>

                <div class="currency-group">
                    <label for="toCurrency">To</label>
                    <select id="toCurrency" name="to_currency" required>
                        <?php foreach ($currencies as $code => $name): ?>
                            <option value="<?php echo htmlspecialchars($code, ENT_QUOTES, 'UTF-8'); ?>"
                                <?php echo $code === $defaultToCurrency ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($code . ' - ' . $name, ENT_QUOTES, 'UTF-8'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <button type="submit" class="convert-btn">
                Convert Currency
            </button>
        </form>

        <div id="loading" class="loading" role="status" aria-live="polite">
            Converting... Please wait
        </div>

        <div id="result" class="result" role="region" aria-live="polite">
            <!-- Results will be displayed here -->
        </div>

        <div class="app-info">
            <p><strong>PHP <?php echo PHP_VERSION; ?></strong> | Exchange rates updated hourly</p>
            <p>Powered by ExchangeRate-API | Version 2.0</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html><?php
    }
}

// Initialize and handle the request
try {
    $controller = new CurrencyConverterController();
    $controller->handleRequest();
} catch (Throwable $e) {
    error_log('Fatal error in currency converter: ' . $e->getMessage());

    // Show a generic error page
    http_response_code(500);
    echo '<!DOCTYPE html><html><head><title>Error</title></head><body>';
    echo '<h1>Service Temporarily Unavailable</h1>';
    echo '<p>Please try again later.</p>';
    echo '</body></html>';
}
