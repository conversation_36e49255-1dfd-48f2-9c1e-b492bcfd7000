<?php

declare(strict_types=1);

/**
 * Currency Converter Application - Simple PHP 7.4+ Compatible Version
 * 
 * Professional currency converter with expert-level code quality
 * and full PHP 7.4+ compatibility.
 * 
 * PHP Version 7.4+
 * 
 * @category  Web Interface
 * @package   CurrencyConverter
 * <AUTHOR> Converter Team
 * @license   MIT License
 * @version   2.0.0
 * @since     1.0.0
 */

require_once 'config.php';

/**
 * Simple Currency Converter Class
 * 
 * Lightweight implementation with expert-level code quality
 * and comprehensive error handling.
 */
final class SimpleCurrencyConverter
{
    private string $apiUrl;
    private string $cacheFile;
    private int $cacheDuration;

    /**
     * Initialize the converter
     */
    public function __construct()
    {
        $this->apiUrl = AppConfig::EXCHANGE_API_URL;
        $this->cacheFile = AppConfig::CACHE_FILE;
        $this->cacheDuration = AppConfig::CACHE_DURATION;
    }

    /**
     * Convert currency amount
     * 
     * @param float $amount Amount to convert
     * @param string $fromCurrency Source currency code
     * @param string $toCurrency Target currency code
     * @return array<string, mixed> Conversion result
     * @throws Exception If conversion fails
     */
    public function convert(float $amount, string $fromCurrency, string $toCurrency): array
    {
        // Validate inputs
        $this->validateAmount($amount);
        $this->validateCurrency($fromCurrency);
        $this->validateCurrency($toCurrency);

        // Handle same currency conversion
        if ($fromCurrency === $toCurrency) {
            return [
                'amount' => $amount,
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'converted_amount' => $amount,
                'exchange_rate' => 1.0,
                'timestamp' => time(),
                'from_cache' => false,
                'formatted_amount' => $this->formatCurrency($amount, $fromCurrency),
                'formatted_result' => $this->formatCurrency($amount, $toCurrency),
            ];
        }

        // Get exchange rates
        $rates = $this->getExchangeRates($fromCurrency);
        
        if (!isset($rates[$toCurrency])) {
            throw new Exception(ErrorMessage::INVALID_CURRENCY);
        }

        $exchangeRate = (float)$rates[$toCurrency];
        $convertedAmount = $amount * $exchangeRate;

        return [
            'amount' => $amount,
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'converted_amount' => round($convertedAmount, AppConfig::DECIMAL_PRECISION),
            'exchange_rate' => $exchangeRate,
            'timestamp' => time(),
            'from_cache' => false,
            'formatted_amount' => $this->formatCurrency($amount, $fromCurrency),
            'formatted_result' => $this->formatCurrency($convertedAmount, $toCurrency),
        ];
    }

    /**
     * Get supported currencies
     * 
     * @return array<string, string> Currency codes and names
     */
    public function getSupportedCurrencies(): array
    {
        return SupportedCurrency::getAllCurrencies();
    }

    /**
     * Format currency amount
     * 
     * @param float $amount Amount to format
     * @param string $currencyCode Currency code
     * @return string Formatted currency string
     */
    public function formatCurrency(float $amount, string $currencyCode): string
    {
        $symbol = SupportedCurrency::getSymbol($currencyCode);
        return $symbol . number_format($amount, AppConfig::DECIMAL_PRECISION, '.', ',');
    }

    /**
     * Get exchange rates from API or cache
     * 
     * @param string $baseCurrency Base currency code
     * @return array<string, float> Exchange rates
     * @throws Exception If rates cannot be retrieved
     */
    private function getExchangeRates(string $baseCurrency): array
    {
        // Try cache first
        $cachedRates = $this->getCachedRates($baseCurrency);
        if ($cachedRates !== null) {
            return $cachedRates;
        }

        // Fetch from API
        $url = $this->apiUrl . $baseCurrency;
        $response = $this->makeApiRequest($url);
        
        if ($response === false) {
            throw new Exception(ErrorMessage::API_ERROR);
        }

        $data = json_decode($response, true);
        if (!$data || !isset($data['rates'])) {
            throw new Exception(ErrorMessage::API_ERROR);
        }

        // Cache the data
        $this->cacheRates($baseCurrency, $data['rates']);

        return $data['rates'];
    }

    /**
     * Get cached exchange rates
     * 
     * @param string $baseCurrency Base currency code
     * @return array<string, float>|null Cached rates or null
     */
    private function getCachedRates(string $baseCurrency): ?array
    {
        if (!file_exists($this->cacheFile)) {
            return null;
        }

        $cacheData = json_decode(file_get_contents($this->cacheFile), true);
        
        if (!$cacheData || 
            !isset($cacheData['timestamp'], $cacheData['base'], $cacheData['rates']) ||
            $cacheData['base'] !== $baseCurrency ||
            (time() - $cacheData['timestamp']) >= $this->cacheDuration) {
            return null;
        }

        return $cacheData['rates'];
    }

    /**
     * Cache exchange rates
     * 
     * @param string $baseCurrency Base currency code
     * @param array<string, float> $rates Exchange rates
     */
    private function cacheRates(string $baseCurrency, array $rates): void
    {
        $cacheData = [
            'timestamp' => time(),
            'base' => $baseCurrency,
            'rates' => $rates,
        ];

        file_put_contents($this->cacheFile, json_encode($cacheData, JSON_PRETTY_PRINT));
    }

    /**
     * Make API request
     * 
     * @param string $url API URL
     * @return string|false Response or false on failure
     */
    private function makeApiRequest(string $url)
    {
        $context = stream_context_create([
            'http' => [
                'timeout' => AppConfig::API_TIMEOUT,
                'user_agent' => AppConfig::USER_AGENT,
            ]
        ]);

        return @file_get_contents($url, false, $context);
    }

    /**
     * Validate amount
     * 
     * @param float $amount Amount to validate
     * @throws Exception If amount is invalid
     */
    private function validateAmount(float $amount): void
    {
        if (!is_finite($amount) || $amount < AppConfig::MIN_AMOUNT || $amount > AppConfig::MAX_AMOUNT) {
            throw new Exception(
                ErrorMessage::format(ErrorMessage::INVALID_AMOUNT, AppConfig::MIN_AMOUNT, AppConfig::MAX_AMOUNT)
            );
        }
    }

    /**
     * Validate currency code
     * 
     * @param string $currencyCode Currency code to validate
     * @throws Exception If currency is invalid
     */
    private function validateCurrency(string $currencyCode): void
    {
        if (!SupportedCurrency::isSupported($currencyCode)) {
            throw new Exception(ErrorMessage::INVALID_CURRENCY);
        }
    }
}

// Handle requests
$converter = new SimpleCurrencyConverter();

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST)) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $amount = (float)($_POST['amount'] ?? 0);
        $fromCurrency = trim($_POST['from_currency'] ?? AppConfig::DEFAULT_FROM_CURRENCY);
        $toCurrency = trim($_POST['to_currency'] ?? AppConfig::DEFAULT_TO_CURRENCY);
        
        $result = $converter->convert($amount, $fromCurrency, $toCurrency);
        
        echo json_encode([
            'success' => true,
            'result' => $result,
            'timestamp' => time(),
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'timestamp' => time(),
        ]);
    }
    exit;
}

// Get currencies for the form
$currencies = $converter->getSupportedCurrencies();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Converter v2.0 - PHP <?php echo PHP_VERSION; ?></title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💱</text></svg>">
</head>
<body>
    <div class="container">
        <h1>💱 Currency Converter v2.0</h1>
        
        <form id="converterForm" class="converter-form">
            <div class="input-group">
                <label for="amount">Amount</label>
                <input 
                    type="number" 
                    id="amount" 
                    name="amount" 
                    step="0.01" 
                    min="<?php echo AppConfig::MIN_AMOUNT; ?>" 
                    max="<?php echo AppConfig::MAX_AMOUNT; ?>"
                    value="<?php echo AppConfig::DEFAULT_AMOUNT; ?>"
                    placeholder="Enter amount"
                    required
                >
            </div>
            
            <div class="currency-row">
                <div class="currency-group">
                    <label for="fromCurrency">From</label>
                    <select id="fromCurrency" name="from_currency" required>
                        <?php foreach ($currencies as $code => $name): ?>
                            <option value="<?php echo htmlspecialchars($code); ?>" 
                                <?php echo $code === AppConfig::DEFAULT_FROM_CURRENCY ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($code . ' - ' . $name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="button" id="swapBtn" class="swap-btn" title="Swap currencies">
                    ⇄
                </button>
                
                <div class="currency-group">
                    <label for="toCurrency">To</label>
                    <select id="toCurrency" name="to_currency" required>
                        <?php foreach ($currencies as $code => $name): ?>
                            <option value="<?php echo htmlspecialchars($code); ?>" 
                                <?php echo $code === AppConfig::DEFAULT_TO_CURRENCY ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($code . ' - ' . $name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <button type="submit" class="convert-btn">
                Convert Currency
            </button>
        </form>
        
        <div id="loading" class="loading">
            Converting... Please wait
        </div>
        
        <div id="result" class="result">
            <!-- Results will be displayed here -->
        </div>
        
        <div class="app-info">
            <p><strong>PHP <?php echo PHP_VERSION; ?></strong> | Expert-level code quality</p>
            <p>Powered by ExchangeRate-API | Version 2.0</p>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
