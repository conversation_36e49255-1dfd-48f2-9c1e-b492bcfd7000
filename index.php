<?php
/**
 * Currency Converter App - Main Interface
 */

require_once 'CurrencyConverter.php';

$converter = new CurrencyConverter();
$result = null;
$error = null;

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST)) {
    header('Content-Type: application/json');
    
    try {
        $amount = floatval($_POST['amount'] ?? 0);
        $from_currency = $_POST['from_currency'] ?? DEFAULT_FROM_CURRENCY;
        $to_currency = $_POST['to_currency'] ?? DEFAULT_TO_CURRENCY;
        
        $result = $converter->convert($amount, $from_currency, $to_currency);
        
        echo json_encode([
            'success' => true,
            'result' => $result
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

// Get supported currencies for dropdowns
$currencies = $converter->getSupportedCurrencies();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Converter</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💱</text></svg>">
</head>
<body>
    <div class="container">
        <h1>💱 Currency Converter</h1>
        
        <form id="converterForm" class="converter-form">
            <div class="input-group">
                <label for="amount">Amount</label>
                <input 
                    type="number" 
                    id="amount" 
                    name="amount" 
                    step="0.01" 
                    min="0" 
                    value="<?php echo DEFAULT_AMOUNT; ?>"
                    placeholder="Enter amount"
                    required
                >
            </div>
            
            <div class="currency-row">
                <div class="currency-group">
                    <label for="fromCurrency">From</label>
                    <select id="fromCurrency" name="from_currency" required>
                        <?php foreach ($currencies as $code => $name): ?>
                            <option value="<?php echo $code; ?>" 
                                <?php echo $code === DEFAULT_FROM_CURRENCY ? 'selected' : ''; ?>>
                                <?php echo $code; ?> - <?php echo $name; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="button" id="swapBtn" class="swap-btn" title="Swap currencies">
                    ⇄
                </button>
                
                <div class="currency-group">
                    <label for="toCurrency">To</label>
                    <select id="toCurrency" name="to_currency" required>
                        <?php foreach ($currencies as $code => $name): ?>
                            <option value="<?php echo $code; ?>" 
                                <?php echo $code === DEFAULT_TO_CURRENCY ? 'selected' : ''; ?>>
                                <?php echo $code; ?> - <?php echo $name; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <button type="submit" class="convert-btn">
                Convert Currency
            </button>
        </form>
        
        <div id="loading" class="loading">
            Converting... Please wait
        </div>
        
        <div id="result" class="result">
            <!-- Results will be displayed here -->
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #666; font-size: 0.8em;">
            <p>Exchange rates are updated hourly</p>
            <p>Powered by ExchangeRate-API</p>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
