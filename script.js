// Currency Converter JavaScript

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('converterForm');
    const swapBtn = document.getElementById('swapBtn');
    const fromCurrency = document.getElementById('fromCurrency');
    const toCurrency = document.getElementById('toCurrency');
    const amount = document.getElementById('amount');
    const result = document.getElementById('result');
    const loading = document.getElementById('loading');

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        convertCurrency();
    });

    // Handle swap button
    swapBtn.addEventListener('click', function() {
        const fromValue = fromCurrency.value;
        const toValue = toCurrency.value;
        
        fromCurrency.value = toValue;
        toCurrency.value = fromValue;
        
        // Auto-convert if amount is entered
        if (amount.value && amount.value > 0) {
            convertCurrency();
        }
    });

    // Auto-convert on currency change (optional)
    fromCurrency.addEventListener('change', function() {
        if (amount.value && amount.value > 0) {
            convertCurrency();
        }
    });

    toCurrency.addEventListener('change', function() {
        if (amount.value && amount.value > 0) {
            convertCurrency();
        }
    });

    // Auto-convert on amount change with debounce
    let timeoutId;
    amount.addEventListener('input', function() {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(function() {
            if (amount.value && amount.value > 0) {
                convertCurrency();
            }
        }, 500); // Wait 500ms after user stops typing
    });

    function convertCurrency() {
        const formData = new FormData(form);
        
        // Show loading
        loading.classList.add('show');
        result.classList.remove('show');

        // Validate inputs
        if (!amount.value || amount.value <= 0) {
            showError('Please enter a valid amount');
            return;
        }

        fetch('index.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.classList.remove('show');
            
            if (data.success) {
                showResult(data.result);
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            loading.classList.remove('show');
            showError('Network error occurred. Please try again.');
            console.error('Error:', error);
        });
    }

    function showResult(data) {
        const resultHtml = `
            <div class="result-amount">
                ${formatCurrency(data.converted_amount, data.to_currency)}
            </div>
            <div class="result-details">
                ${formatCurrency(data.amount, data.from_currency)} = 
                ${formatCurrency(data.converted_amount, data.to_currency)}
                <div class="rate-info">
                    Exchange Rate: 1 ${data.from_currency} = ${data.exchange_rate} ${data.to_currency}
                </div>
            </div>
        `;
        
        result.innerHTML = resultHtml;
        result.className = 'result show';
    }

    function showError(message) {
        result.innerHTML = `<div class="result-amount">Error</div><div class="result-details">${message}</div>`;
        result.className = 'result error show';
    }

    function formatCurrency(amount, currencyCode) {
        const symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥',
            'CNY': '¥',
            'INR': '₹',
            'KRW': '₩',
            'RUB': '₽'
        };

        const symbol = symbols[currencyCode] || currencyCode + ' ';
        return symbol + parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    // Initialize with default conversion if amount is set
    if (amount.value && amount.value > 0) {
        convertCurrency();
    }
});

// Add some visual enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.convert-btn, .swap-btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .convert-btn, .swap-btn {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
