# Currency Converter App

A modern, responsive currency converter web application built with PHP, HTML, CSS, and JavaScript.

## Features

- **Real-time Currency Conversion**: Convert between 45+ world currencies
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Smart Caching**: Reduces API calls by caching exchange rates for 1 hour
- **Auto-conversion**: Automatically converts as you type (with debounce)
- **Swap Functionality**: Easily swap between currencies with one click
- **Error Handling**: Graceful error handling for network issues and API failures
- **Modern UI**: Clean, professional interface with smooth animations

## Supported Currencies

The app supports major world currencies including:
- USD (US Dollar)
- EUR (Euro)
- GBP (British Pound)
- JPY (Japanese Yen)
- AUD (Australian Dollar)
- CAD (Canadian Dollar)
- CHF (Swiss Franc)
- CNY (Chinese Yuan)
- And many more...

## Installation

1. **Clone or download** the project files to your web server directory
2. **Ensure PHP is installed** (PHP 7.0 or higher recommended)
3. **Make sure the cache directory is writable** by the web server
4. **Access the application** through your web browser

### Requirements

- PHP 7.0+
- Web server (Apache, Nginx, or built-in PHP server)
- Internet connection for fetching exchange rates

## Usage

1. **Enter the amount** you want to convert
2. **Select the source currency** from the "From" dropdown
3. **Select the target currency** from the "To" dropdown
4. **Click "Convert Currency"** or wait for auto-conversion
5. **View the result** with exchange rate information

### Quick Actions

- **Swap currencies**: Click the ⇄ button between currency selectors
- **Auto-convert**: The app automatically converts as you type (after 500ms delay)
- **Real-time updates**: Exchange rates are cached for 1 hour and updated automatically

## File Structure

```
currency-converter/
├── index.php              # Main application file
├── CurrencyConverter.php   # Core converter class
├── config.php             # Configuration settings
├── styles.css             # Application styling
├── script.js              # Frontend JavaScript
├── README.md              # This file
└── cache/                 # Cache directory (auto-created)
    └── exchange_rates.json # Cached exchange rates
```

## Configuration

Edit `config.php` to customize:

- **API Settings**: Change the exchange rate API endpoint
- **Cache Duration**: Modify how long rates are cached (default: 1 hour)
- **Supported Currencies**: Add or remove currencies
- **Default Values**: Set default currencies and amount
- **Error Messages**: Customize error messages

## API Information

This app uses the free tier of ExchangeRate-API which provides:
- 1,500 free requests per month
- Real-time exchange rates
- 150+ currencies supported
- No API key required for basic usage

## Development

### Running Locally

You can run the application using PHP's built-in server:

```bash
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

### Customization

- **Styling**: Modify `styles.css` to change the appearance
- **Functionality**: Edit `script.js` for frontend behavior
- **Backend Logic**: Update `CurrencyConverter.php` for conversion logic
- **Configuration**: Adjust settings in `config.php`

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Security Features

- Input validation and sanitization
- Error handling for malformed requests
- Safe file operations for caching
- XSS protection through proper output escaping

## Performance

- **Caching**: Exchange rates are cached to reduce API calls
- **Optimized requests**: Only fetches new data when cache expires
- **Lightweight**: Minimal dependencies and fast loading
- **Responsive**: Optimized for all device sizes

## Troubleshooting

### Common Issues

1. **"Unable to fetch exchange rates"**
   - Check internet connection
   - Verify API endpoint is accessible
   - Check if cache directory is writable

2. **Conversion not working**
   - Ensure JavaScript is enabled
   - Check browser console for errors
   - Verify form inputs are valid

3. **Styling issues**
   - Clear browser cache
   - Check if CSS file is loading properly
   - Verify file permissions

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, fork the repository, and create pull requests for any improvements.

## Support

For support or questions, please create an issue in the project repository.
