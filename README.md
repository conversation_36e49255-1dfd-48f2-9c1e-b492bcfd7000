# Currency Converter App v2.0

A professional, enterprise-grade currency converter web application built with modern PHP 8.4+, featuring advanced architecture, comprehensive error handling, and expert-level code quality.

## 🚀 Features

### Core Functionality
- **Real-time Currency Conversion**: Convert between 45+ world currencies with live exchange rates
- **Advanced Caching System**: Intelligent caching with automatic expiration and fallback mechanisms
- **Type-Safe Architecture**: Full PHP 8.4 compatibility with strict typing, enums, and modern features
- **Robust Error Handling**: Comprehensive exception handling with detailed logging and user-friendly messages
- **Security-First Design**: Input validation, XSS protection, and security headers

### User Experience
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Auto-conversion**: Real-time conversion as you type with intelligent debouncing
- **Currency Swap**: One-click currency swapping with smooth animations
- **Accessibility**: ARIA labels, keyboard navigation, and screen reader support
- **Professional UI**: Modern interface with smooth animations and visual feedback

### Technical Excellence
- **PHP 8.4 Compatible**: Uses latest PHP features including enums, readonly classes, and typed properties
- **Expert Code Quality**: Comprehensive PHPDoc comments, PSR-12 coding standards, and SOLID principles
- **Dependency Injection**: Clean architecture with proper separation of concerns
- **Performance Optimized**: Efficient caching, minimal API calls, and optimized frontend

## Supported Currencies

The app supports major world currencies including:
- USD (US Dollar)
- EUR (Euro)
- GBP (British Pound)
- JPY (Japanese Yen)
- AUD (Australian Dollar)
- CAD (Canadian Dollar)
- CHF (Swiss Franc)
- CNY (Chinese Yuan)
- And many more...

## 📋 Requirements

### System Requirements
- **PHP 8.4+** (Required for modern features)
- **Web Server**: Apache, Nginx, or PHP built-in server
- **Extensions**: JSON, cURL (for API requests)
- **Permissions**: Write access for cache directory
- **Internet Connection**: For fetching live exchange rates

### Recommended Environment
- **PHP 8.4** with OPcache enabled
- **Memory**: 128MB+ PHP memory limit
- **Storage**: 10MB+ free space for cache
- **SSL Certificate**: For production deployments

## 🚀 Installation

### Quick Start
1. **Download/Clone** the project files to your web server directory
2. **Set Permissions**: Ensure the web server can write to the cache directory
   ```bash
   chmod 755 cache/
   ```
3. **Configure PHP**: Verify PHP 8.4+ is installed with required extensions
   ```bash
   php --version
   php -m | grep -E "(json|curl)"
   ```
4. **Access Application**: Open in your web browser

### Production Setup
1. **Environment Configuration**:
   - Set appropriate file permissions (644 for files, 755 for directories)
   - Configure proper error logging
   - Enable OPcache for better performance

2. **Security Hardening**:
   - Use HTTPS in production
   - Configure proper CSP headers
   - Regular security updates

## Usage

1. **Enter the amount** you want to convert
2. **Select the source currency** from the "From" dropdown
3. **Select the target currency** from the "To" dropdown
4. **Click "Convert Currency"** or wait for auto-conversion
5. **View the result** with exchange rate information

### Quick Actions

- **Swap currencies**: Click the ⇄ button between currency selectors
- **Auto-convert**: The app automatically converts as you type (after 500ms delay)
- **Real-time updates**: Exchange rates are cached for 1 hour and updated automatically

## 📁 File Structure

```
currency-converter-v2/
├── index.php                    # Main application controller with MVC pattern
├── CurrencyConverter.php        # Core conversion engine with modern PHP 8.4 features
│   ├── ConversionResult         # Readonly DTO for conversion results
│   ├── ExchangeRateCache        # Advanced caching system
│   ├── ExchangeRateApiClient    # HTTP client with retry logic
│   ├── CurrencyConverterException # Custom exception handling
│   └── CurrencyConverter        # Main service class
├── config.php                   # Configuration with enums and typed constants
│   ├── AppConfig               # Application configuration class
│   ├── SupportedCurrency       # Currency enumeration
│   ├── ErrorMessage            # Error message enumeration
│   └── initializeEnvironment() # Environment setup function
├── styles.css                   # Enhanced responsive styling
├── script.js                    # Modern JavaScript with error handling
├── README.md                    # Comprehensive documentation
└── cache/                       # Auto-created cache directory
    └── exchange_rates.json      # Structured cache with metadata
```

### Architecture Highlights

- **Separation of Concerns**: Clear separation between presentation, business logic, and data layers
- **Type Safety**: Full type declarations with PHP 8.4 features
- **Error Handling**: Comprehensive exception hierarchy with context
- **Caching Strategy**: Multi-layer caching with fallback mechanisms
- **Security**: Input validation, output escaping, and security headers

## Configuration

Edit `config.php` to customize:

- **API Settings**: Change the exchange rate API endpoint
- **Cache Duration**: Modify how long rates are cached (default: 1 hour)
- **Supported Currencies**: Add or remove currencies
- **Default Values**: Set default currencies and amount
- **Error Messages**: Customize error messages

## API Information

This app uses the free tier of ExchangeRate-API which provides:
- 1,500 free requests per month
- Real-time exchange rates
- 150+ currencies supported
- No API key required for basic usage

## Development

### Running Locally

You can run the application using PHP's built-in server:

```bash
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

### Customization

- **Styling**: Modify `styles.css` to change the appearance
- **Functionality**: Edit `script.js` for frontend behavior
- **Backend Logic**: Update `CurrencyConverter.php` for conversion logic
- **Configuration**: Adjust settings in `config.php`

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Security Features

- Input validation and sanitization
- Error handling for malformed requests
- Safe file operations for caching
- XSS protection through proper output escaping

## Performance

- **Caching**: Exchange rates are cached to reduce API calls
- **Optimized requests**: Only fetches new data when cache expires
- **Lightweight**: Minimal dependencies and fast loading
- **Responsive**: Optimized for all device sizes

## Troubleshooting

### Common Issues

1. **"Unable to fetch exchange rates"**
   - Check internet connection
   - Verify API endpoint is accessible
   - Check if cache directory is writable

2. **Conversion not working**
   - Ensure JavaScript is enabled
   - Check browser console for errors
   - Verify form inputs are valid

3. **Styling issues**
   - Clear browser cache
   - Check if CSS file is loading properly
   - Verify file permissions

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, fork the repository, and create pull requests for any improvements.

## Support

For support or questions, please create an issue in the project repository.
