/* Currency Converter App Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 500px;
    position: relative;
    overflow: hidden;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2.2em;
    font-weight: 300;
}

.converter-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

label {
    font-weight: 600;
    color: #555;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

input[type="number"],
select {
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

input[type="number"]:focus,
select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.currency-row {
    display: flex;
    gap: 15px;
    align-items: end;
}

.currency-group {
    flex: 1;
}

.swap-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-bottom: 2px;
}

.swap-btn:hover {
    background: #5a6fd8;
    transform: rotate(180deg);
}

.convert-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.convert-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.convert-btn:active {
    transform: translateY(0);
}

.result {
    margin-top: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    border-left: 5px solid #667eea;
    display: none;
}

.result.show {
    display: block;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-amount {
    font-size: 1.8em;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.result-details {
    color: #666;
    font-size: 0.9em;
    line-height: 1.5;
}

.error {
    background: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.loading {
    display: none;
    text-align: center;
    color: #667eea;
    font-style: italic;
}

.loading.show {
    display: block;
}

/* Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 30px 20px;
        margin: 10px;
    }
    
    h1 {
        font-size: 1.8em;
    }
    
    .currency-row {
        flex-direction: column;
        gap: 20px;
    }
    
    .swap-btn {
        align-self: center;
        transform: rotate(90deg);
    }
    
    .swap-btn:hover {
        transform: rotate(270deg);
    }
}

/* Currency flag icons (optional enhancement) */
.currency-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rate-info {
    background: #e3f2fd;
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    font-size: 0.85em;
    color: #1565c0;
}
