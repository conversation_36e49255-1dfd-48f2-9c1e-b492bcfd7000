<?php

declare(strict_types=1);

/**
 * Currency Converter Test Script
 * 
 * Simple test script to verify PHP 8.4 compatibility and functionality
 * 
 * PHP Version 8.4+
 * 
 * @category  Testing
 * @package   CurrencyConverter
 * <AUTHOR> Converter Team
 * @license   MIT License
 * @version   2.0.0
 */

require_once 'CurrencyConverter.php';

/**
 * Test runner for currency converter functionality
 */
final class CurrencyConverterTest
{
    private readonly CurrencyConverter $converter;

    public function __construct()
    {
        $this->converter = new CurrencyConverter();
    }

    /**
     * Run all tests
     * 
     * @return void
     */
    public function runTests(): void
    {
        echo "🧪 Currency Converter v2.0 Test Suite\n";
        echo "=====================================\n\n";

        $this->testPhpVersion();
        $this->testConfiguration();
        $this->testCurrencyEnum();
        $this->testBasicConversion();
        $this->testSameCurrencyConversion();
        $this->testInvalidInputs();
        $this->testCurrencyFormatting();

        echo "\n✅ All tests completed!\n";
    }

    /**
     * Test PHP version compatibility
     */
    private function testPhpVersion(): void
    {
        echo "📋 Testing PHP Version Compatibility...\n";
        
        $version = PHP_VERSION;
        $majorVersion = (int)explode('.', $version)[0];
        $minorVersion = (int)explode('.', $version)[1];
        
        if ($majorVersion >= 8 && $minorVersion >= 1) {
            echo "   ✅ PHP $version - Compatible with modern features\n";
        } else {
            echo "   ⚠️  PHP $version - Some features may not work\n";
        }
        
        // Test required extensions
        $extensions = ['json', 'curl'];
        foreach ($extensions as $ext) {
            if (extension_loaded($ext)) {
                echo "   ✅ Extension '$ext' loaded\n";
            } else {
                echo "   ❌ Extension '$ext' missing\n";
            }
        }
        
        echo "\n";
    }

    /**
     * Test configuration classes
     */
    private function testConfiguration(): void
    {
        echo "⚙️  Testing Configuration...\n";
        
        // Test AppConfig constants
        echo "   ✅ API URL: " . AppConfig::EXCHANGE_API_URL . "\n";
        echo "   ✅ Cache Duration: " . AppConfig::CACHE_DURATION . " seconds\n";
        echo "   ✅ Default Currency: " . AppConfig::DEFAULT_FROM_CURRENCY . " -> " . AppConfig::DEFAULT_TO_CURRENCY . "\n";
        
        // Test cache directory
        if (is_dir(AppConfig::CACHE_DIRECTORY)) {
            echo "   ✅ Cache directory exists\n";
        } else {
            echo "   ❌ Cache directory missing\n";
        }
        
        echo "\n";
    }

    /**
     * Test currency enumeration
     */
    private function testCurrencyEnum(): void
    {
        echo "💱 Testing Currency Enumeration...\n";
        
        // Test enum functionality
        $usd = SupportedCurrency::USD;
        echo "   ✅ USD enum value: " . $usd->value . "\n";
        echo "   ✅ USD full name: " . $usd->getFullName() . "\n";
        echo "   ✅ USD symbol: " . $usd->getSymbol() . "\n";
        
        // Test currency validation
        if (SupportedCurrency::isSupported('USD')) {
            echo "   ✅ Currency validation works\n";
        } else {
            echo "   ❌ Currency validation failed\n";
        }
        
        $currencies = SupportedCurrency::getAllCurrencies();
        echo "   ✅ Total supported currencies: " . count($currencies) . "\n";
        
        echo "\n";
    }

    /**
     * Test basic currency conversion
     */
    private function testBasicConversion(): void
    {
        echo "🔄 Testing Basic Conversion...\n";
        
        try {
            // Note: This will fail without internet connection
            $result = $this->converter->convert(100.0, 'USD', 'EUR');
            
            echo "   ✅ Conversion successful\n";
            echo "   ✅ Original: " . $result->getFormattedAmount() . "\n";
            echo "   ✅ Converted: " . $result->getFormattedResult() . "\n";
            echo "   ✅ Rate: " . $result->exchangeRate . "\n";
            echo "   ✅ From cache: " . ($result->fromCache ? 'Yes' : 'No') . "\n";
            
        } catch (CurrencyConverterException $e) {
            echo "   ⚠️  Conversion failed (expected without internet): " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * Test same currency conversion
     */
    private function testSameCurrencyConversion(): void
    {
        echo "🔄 Testing Same Currency Conversion...\n";
        
        try {
            $result = $this->converter->convert(100.0, 'USD', 'USD');
            
            if ($result->convertedAmount === 100.0 && $result->exchangeRate === 1.0) {
                echo "   ✅ Same currency conversion works correctly\n";
            } else {
                echo "   ❌ Same currency conversion failed\n";
            }
            
        } catch (CurrencyConverterException $e) {
            echo "   ❌ Same currency conversion error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * Test invalid inputs
     */
    private function testInvalidInputs(): void
    {
        echo "🚫 Testing Invalid Inputs...\n";
        
        // Test invalid currency
        try {
            $this->converter->convert(100.0, 'INVALID', 'USD');
            echo "   ❌ Invalid currency should have thrown exception\n";
        } catch (CurrencyConverterException $e) {
            echo "   ✅ Invalid currency properly rejected\n";
        }
        
        // Test invalid amount
        try {
            $this->converter->convert(-100.0, 'USD', 'EUR');
            echo "   ❌ Negative amount should have thrown exception\n";
        } catch (CurrencyConverterException $e) {
            echo "   ✅ Negative amount properly rejected\n";
        }
        
        echo "\n";
    }

    /**
     * Test currency formatting
     */
    private function testCurrencyFormatting(): void
    {
        echo "💰 Testing Currency Formatting...\n";
        
        try {
            $formatted = $this->converter->formatCurrency(1234.56, 'USD');
            echo "   ✅ USD formatting: $formatted\n";
            
            $formatted = $this->converter->formatCurrency(1234.56, 'EUR');
            echo "   ✅ EUR formatting: $formatted\n";
            
            $formatted = $this->converter->formatCurrency(1234.56, 'JPY');
            echo "   ✅ JPY formatting: $formatted\n";
            
        } catch (CurrencyConverterException $e) {
            echo "   ❌ Formatting error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
}

// Run tests if script is executed directly
if (php_sapi_name() === 'cli') {
    $test = new CurrencyConverterTest();
    $test->runTests();
} else {
    echo "<!DOCTYPE html><html><head><title>Test Results</title></head><body>";
    echo "<h1>Currency Converter Test Results</h1>";
    echo "<pre>";
    
    ob_start();
    $test = new CurrencyConverterTest();
    $test->runTests();
    $output = ob_get_clean();
    
    echo htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
    echo "</pre></body></html>";
}
