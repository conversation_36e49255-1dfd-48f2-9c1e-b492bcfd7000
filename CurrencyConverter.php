<?php
/**
 * Currency Converter Class
 * Handles currency conversion using external API with caching
 */

require_once 'config.php';

class CurrencyConverter {
    private $api_url;
    private $cache_file;
    private $cache_duration;
    private $supported_currencies;
    private $error_messages;

    public function __construct() {
        global $supported_currencies, $error_messages;
        
        $this->api_url = EXCHANGE_API_URL;
        $this->cache_file = CACHE_FILE;
        $this->cache_duration = CACHE_DURATION;
        $this->supported_currencies = $supported_currencies;
        $this->error_messages = $error_messages;
    }

    /**
     * Get exchange rates from API or cache
     */
    private function getExchangeRates($base_currency = 'USD') {
        // Check if cache exists and is still valid
        if (file_exists($this->cache_file)) {
            $cache_data = json_decode(file_get_contents($this->cache_file), true);
            
            if ($cache_data && 
                isset($cache_data['timestamp']) && 
                isset($cache_data['base']) &&
                $cache_data['base'] === $base_currency &&
                (time() - $cache_data['timestamp']) < $this->cache_duration) {
                return $cache_data['rates'];
            }
        }

        // Fetch fresh data from API
        $url = $this->api_url . $base_currency;
        $response = $this->makeApiRequest($url);

        if ($response === false) {
            // If API fails, try to use cached data even if expired
            if (file_exists($this->cache_file)) {
                $cache_data = json_decode(file_get_contents($this->cache_file), true);
                if ($cache_data && isset($cache_data['rates'])) {
                    return $cache_data['rates'];
                }
            }
            throw new Exception($this->error_messages['api_error']);
        }

        $data = json_decode($response, true);
        
        if (!$data || !isset($data['rates'])) {
            throw new Exception($this->error_messages['api_error']);
        }

        // Cache the data
        $cache_data = [
            'timestamp' => time(),
            'base' => $base_currency,
            'rates' => $data['rates']
        ];

        file_put_contents($this->cache_file, json_encode($cache_data));

        return $data['rates'];
    }

    /**
     * Make API request with error handling
     */
    private function makeApiRequest($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Currency Converter App'
            ]
        ]);

        return @file_get_contents($url, false, $context);
    }

    /**
     * Convert currency amount
     */
    public function convert($amount, $from_currency, $to_currency) {
        // Validate inputs
        if (!is_numeric($amount) || $amount < 0) {
            throw new Exception($this->error_messages['invalid_amount']);
        }

        if (!isset($this->supported_currencies[$from_currency]) || 
            !isset($this->supported_currencies[$to_currency])) {
            throw new Exception($this->error_messages['invalid_currency']);
        }

        // If same currency, return the same amount
        if ($from_currency === $to_currency) {
            return [
                'amount' => $amount,
                'from_currency' => $from_currency,
                'to_currency' => $to_currency,
                'converted_amount' => $amount,
                'exchange_rate' => 1,
                'timestamp' => time()
            ];
        }

        // Get exchange rates
        $rates = $this->getExchangeRates($from_currency);

        if (!isset($rates[$to_currency])) {
            throw new Exception($this->error_messages['invalid_currency']);
        }

        $exchange_rate = $rates[$to_currency];
        $converted_amount = $amount * $exchange_rate;

        return [
            'amount' => $amount,
            'from_currency' => $from_currency,
            'to_currency' => $to_currency,
            'converted_amount' => round($converted_amount, 2),
            'exchange_rate' => $exchange_rate,
            'timestamp' => time()
        ];
    }

    /**
     * Get list of supported currencies
     */
    public function getSupportedCurrencies() {
        return $this->supported_currencies;
    }

    /**
     * Get currency name by code
     */
    public function getCurrencyName($currency_code) {
        return isset($this->supported_currencies[$currency_code]) 
            ? $this->supported_currencies[$currency_code] 
            : $currency_code;
    }

    /**
     * Format currency amount
     */
    public function formatCurrency($amount, $currency_code) {
        $currency_symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CNY' => '¥',
            'INR' => '₹',
            'KRW' => '₩',
            'RUB' => '₽'
        ];

        $symbol = isset($currency_symbols[$currency_code]) 
            ? $currency_symbols[$currency_code] 
            : $currency_code . ' ';

        return $symbol . number_format($amount, 2);
    }
}
?>
