<?php

declare(strict_types=1);

/**
 * Currency Converter Application - Core Conversion Engine
 *
 * Advanced currency conversion system with intelligent caching,
 * robust error handling, and modern PHP 8.4 features.
 *
 * PHP Version 8.4+
 *
 * @category  Finance
 * @package   CurrencyConverter
 * <AUTHOR> Converter Team
 * @license   MIT License
 * @version   2.0.0
 * @since     1.0.0
 */

require_once 'config.php';

/**
 * Currency Conversion Result Data Transfer Object
 *
 * Immutable value object representing the result of a currency conversion.
 * Uses PHP 8.0+ constructor property promotion for cleaner code.
 */
final readonly class ConversionResult
{
    /**
     * Create a new conversion result
     *
     * @param float $originalAmount The original amount to convert
     * @param SupportedCurrency $fromCurrency Source currency
     * @param SupportedCurrency $toCurrency Target currency
     * @param float $convertedAmount The converted amount
     * @param float $exchangeRate The exchange rate used
     * @param int $timestamp Unix timestamp of conversion
     * @param bool $fromCache Whether the rate was retrieved from cache
     */
    public function __construct(
        public float $originalAmount,
        public SupportedCurrency $fromCurrency,
        public SupportedCurrency $toCurrency,
        public float $convertedAmount,
        public float $exchangeRate,
        public int $timestamp,
        public bool $fromCache = false
    ) {}

    /**
     * Convert to array for JSON serialization
     *
     * @return array<string, mixed> Array representation
     */
    public function toArray(): array
    {
        return [
            'amount' => $this->originalAmount,
            'from_currency' => $this->fromCurrency->value,
            'to_currency' => $this->toCurrency->value,
            'converted_amount' => round($this->convertedAmount, AppConfig::DECIMAL_PRECISION),
            'exchange_rate' => $this->exchangeRate,
            'timestamp' => $this->timestamp,
            'from_cache' => $this->fromCache,
            'formatted_amount' => $this->getFormattedAmount(),
            'formatted_result' => $this->getFormattedResult(),
        ];
    }

    /**
     * Get formatted original amount with currency symbol
     *
     * @return string Formatted amount string
     */
    public function getFormattedAmount(): string
    {
        return $this->fromCurrency->getSymbol() . number_format(
            $this->originalAmount,
            AppConfig::DECIMAL_PRECISION
        );
    }

    /**
     * Get formatted converted amount with currency symbol
     *
     * @return string Formatted result string
     */
    public function getFormattedResult(): string
    {
        return $this->toCurrency->getSymbol() . number_format(
            $this->convertedAmount,
            AppConfig::DECIMAL_PRECISION
        );
    }
}

/**
 * Exchange Rate Cache Manager
 *
 * Handles caching of exchange rates with automatic expiration
 * and fallback mechanisms for improved reliability.
 */
final class ExchangeRateCache
{
    /**
     * Create a new cache manager instance
     *
     * @param string $cacheFile Path to cache file
     * @param int $cacheDuration Cache duration in seconds
     */
    public function __construct(
        private readonly string $cacheFile,
        private readonly int $cacheDuration
    ) {}

    /**
     * Get cached exchange rates for a base currency
     *
     * @param SupportedCurrency $baseCurrency Base currency for rates
     * @return array<string, float>|null Cached rates or null if not found/expired
     */
    public function get(SupportedCurrency $baseCurrency): ?array
    {
        if (!file_exists($this->cacheFile)) {
            return null;
        }

        try {
            $cacheContent = file_get_contents($this->cacheFile);
            if ($cacheContent === false) {
                return null;
            }

            $cacheData = json_decode($cacheContent, true, 512, JSON_THROW_ON_ERROR);

            if (!$this->isValidCacheData($cacheData, $baseCurrency)) {
                return null;
            }

            return $cacheData['rates'];
        } catch (JsonException) {
            // Invalid JSON, remove corrupted cache
            $this->invalidate();
            return null;
        }
    }

    /**
     * Store exchange rates in cache
     *
     * @param SupportedCurrency $baseCurrency Base currency for rates
     * @param array<string, float> $rates Exchange rates to cache
     * @throws CurrencyConverterException If caching fails
     */
    public function store(SupportedCurrency $baseCurrency, array $rates): void
    {
        $cacheData = [
            'timestamp' => time(),
            'base' => $baseCurrency->value,
            'rates' => $rates,
            'version' => '2.0',
        ];

        try {
            $jsonData = json_encode($cacheData, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT);

            if (file_put_contents($this->cacheFile, $jsonData, LOCK_EX) === false) {
                throw new CurrencyConverterException(
                    ErrorMessage::CACHE_ERROR->value
                );
            }

            chmod($this->cacheFile, AppConfig::CACHE_FILE_PERMISSIONS);
        } catch (JsonException $e) {
            throw new CurrencyConverterException(
                ErrorMessage::CACHE_ERROR->value,
                previous: $e
            );
        }
    }

    /**
     * Invalidate (remove) the cache file
     */
    public function invalidate(): void
    {
        if (file_exists($this->cacheFile)) {
            unlink($this->cacheFile);
        }
    }

    /**
     * Validate cache data structure and expiration
     *
     * @param mixed $cacheData Decoded cache data
     * @param SupportedCurrency $baseCurrency Expected base currency
     * @return bool True if cache data is valid and not expired
     */
    private function isValidCacheData(mixed $cacheData, SupportedCurrency $baseCurrency): bool
    {
        if (!is_array($cacheData)) {
            return false;
        }

        $requiredKeys = ['timestamp', 'base', 'rates'];
        foreach ($requiredKeys as $key) {
            if (!isset($cacheData[$key])) {
                return false;
            }
        }

        // Check if cache is expired
        if ((time() - $cacheData['timestamp']) >= $this->cacheDuration) {
            return false;
        }

        // Check if base currency matches
        if ($cacheData['base'] !== $baseCurrency->value) {
            return false;
        }

        return is_array($cacheData['rates']);
    }

    /**
     * Get exchange rates from API or cache
     */
    private function getExchangeRates($base_currency = 'USD') {
        // Check if cache exists and is still valid
        if (file_exists($this->cache_file)) {
            $cache_data = json_decode(file_get_contents($this->cache_file), true);

            if ($cache_data &&
                isset($cache_data['timestamp']) &&
                isset($cache_data['base']) &&
                $cache_data['base'] === $base_currency &&
                (time() - $cache_data['timestamp']) < $this->cache_duration) {
                return $cache_data['rates'];
            }
        }

        // Fetch fresh data from API
        $url = $this->api_url . $base_currency;
        $response = $this->makeApiRequest($url);

        if ($response === false) {
            // If API fails, try to use cached data even if expired
            if (file_exists($this->cache_file)) {
                $cache_data = json_decode(file_get_contents($this->cache_file), true);
                if ($cache_data && isset($cache_data['rates'])) {
                    return $cache_data['rates'];
                }
            }
            throw new Exception($this->error_messages['api_error']);
        }

        $data = json_decode($response, true);

        if (!$data || !isset($data['rates'])) {
            throw new Exception($this->error_messages['api_error']);
        }

        // Cache the data
        $cache_data = [
            'timestamp' => time(),
            'base' => $base_currency,
            'rates' => $data['rates']
        ];

        file_put_contents($this->cache_file, json_encode($cache_data));

        return $data['rates'];
    }

    /**
     * Make API request with error handling
     */
    private function makeApiRequest($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Currency Converter App'
            ]
        ]);

        $response = @file_get_contents($url, false, $context);

        if ($response === false) {
            $error = error_get_last();
            throw new CurrencyConverterException(
                ErrorMessage::NETWORK_ERROR->value,
                context: [
                    'url' => $url,
                    'error' => $error['message'] ?? 'Unknown error',
                ]
            );
        }

        // Check HTTP response code
        if (isset($http_response_header)) {
            $statusLine = $http_response_header[0] ?? '';
            if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $statusLine, $matches)) {
                $statusCode = (int)$matches[1];
                if ($statusCode >= 400) {
                    throw new CurrencyConverterException(
                        $statusCode === 429
                            ? ErrorMessage::RATE_LIMIT_EXCEEDED->value
                            : ErrorMessage::API_ERROR->value,
                        code: $statusCode,
                        context: ['url' => $url, 'status_code' => $statusCode]
                    );
                }
            }
        }

        return $response;
    }

    /**
     * Parse JSON response with error handling
     *
     * @param string $response Raw response body
     * @return array<string, mixed> Parsed response data
     * @throws CurrencyConverterException If parsing fails
     */
    private function parseResponse(string $response): array
    {
        try {
            $data = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

            if (!is_array($data)) {
                throw new CurrencyConverterException(
                    ErrorMessage::API_ERROR->value,
                    context: ['response' => $response]
                );
            }

            return $data;
        } catch (JsonException $e) {
            throw new CurrencyConverterException(
                ErrorMessage::API_ERROR->value,
                previous: $e,
                context: ['response' => substr($response, 0, 500)]
            );
        }
    }
}

/**
 * Main Currency Converter Service
 *
 * High-level service class that orchestrates currency conversion operations
 * using modern PHP 8.4 features, dependency injection, and robust error handling.
 */
final class CurrencyConverter
{
    private readonly ExchangeRateCache $cache;
    private readonly ExchangeRateApiClient $apiClient;

    /**
     * Create a new currency converter instance
     *
     * @param ExchangeRateCache|null $cache Cache manager (auto-created if null)
     * @param ExchangeRateApiClient|null $apiClient API client (auto-created if null)
     */
    public function __construct(
        ?ExchangeRateCache $cache = null,
        ?ExchangeRateApiClient $apiClient = null
    ) {
        $this->cache = $cache ?? new ExchangeRateCache(
            AppConfig::CACHE_FILE,
            AppConfig::CACHE_DURATION
        );

        $this->apiClient = $apiClient ?? new ExchangeRateApiClient(
            AppConfig::EXCHANGE_API_URL
        );
    }

    /**
     * Convert currency amount with comprehensive validation and error handling
     *
     * @param float $amount Amount to convert
     * @param string $fromCurrencyCode Source currency code
     * @param string $toCurrencyCode Target currency code
     * @return ConversionResult Conversion result with all details
     * @throws CurrencyConverterException If conversion fails
     */
    public function convert(
        float $amount,
        string $fromCurrencyCode,
        string $toCurrencyCode
    ): ConversionResult {
        // Validate and parse currency codes
        $fromCurrency = $this->validateCurrency($fromCurrencyCode);
        $toCurrency = $this->validateCurrency($toCurrencyCode);

        // Validate amount
        $this->validateAmount($amount);

        // Handle same currency conversion
        if ($fromCurrency === $toCurrency) {
            return new ConversionResult(
                originalAmount: $amount,
                fromCurrency: $fromCurrency,
                toCurrency: $toCurrency,
                convertedAmount: $amount,
                exchangeRate: 1.0,
                timestamp: time(),
                fromCache: false
            );
        }

        // Get exchange rates (from cache or API)
        $ratesData = $this->getExchangeRates($fromCurrency);
        $rates = $ratesData['rates'];
        $fromCache = $ratesData['from_cache'];

        // Validate that target currency rate exists
        if (!isset($rates[$toCurrency->value])) {
            throw new CurrencyConverterException(
                ErrorMessage::INVALID_CURRENCY->value,
                context: [
                    'currency' => $toCurrency->value,
                    'available_currencies' => array_keys($rates),
                ]
            );
        }

        $exchangeRate = (float)$rates[$toCurrency->value];
        $convertedAmount = $amount * $exchangeRate;

        return new ConversionResult(
            originalAmount: $amount,
            fromCurrency: $fromCurrency,
            toCurrency: $toCurrency,
            convertedAmount: $convertedAmount,
            exchangeRate: $exchangeRate,
            timestamp: time(),
            fromCache: $fromCache
        );
    }

    /**
     * Get all supported currencies
     *
     * @return array<string, string> Array of currency codes and names
     */
    public function getSupportedCurrencies(): array
    {
        return SupportedCurrency::getAllCurrencies();
    }

    /**
     * Get currency information by code
     *
     * @param string $currencyCode Currency code to look up
     * @return array<string, string> Currency information
     * @throws CurrencyConverterException If currency is not supported
     */
    public function getCurrencyInfo(string $currencyCode): array
    {
        $currency = $this->validateCurrency($currencyCode);

        return [
            'code' => $currency->value,
            'name' => $currency->getFullName(),
            'symbol' => $currency->getSymbol(),
        ];
    }

    /**
     * Format currency amount with proper symbol and formatting
     *
     * @param float $amount Amount to format
     * @param string $currencyCode Currency code
     * @return string Formatted currency string
     * @throws CurrencyConverterException If currency is not supported
     */
    public function formatCurrency(float $amount, string $currencyCode): string
    {
        $currency = $this->validateCurrency($currencyCode);

        return $currency->getSymbol() . number_format(
            $amount,
            AppConfig::DECIMAL_PRECISION,
            '.',
            ','
        );
    }

    /**
     * Clear the exchange rate cache
     */
    public function clearCache(): void
    {
        $this->cache->invalidate();
    }

    /**
     * Get exchange rates from cache or API
     *
     * @param SupportedCurrency $baseCurrency Base currency for rates
     * @return array{rates: array<string, float>, from_cache: bool} Rates and cache status
     * @throws CurrencyConverterException If rates cannot be retrieved
     */
    private function getExchangeRates(SupportedCurrency $baseCurrency): array
    {
        // Try to get from cache first
        $cachedRates = $this->cache->get($baseCurrency);
        if ($cachedRates !== null) {
            return [
                'rates' => $cachedRates,
                'from_cache' => true,
            ];
        }

        // Fetch from API
        try {
            $rates = $this->apiClient->fetchExchangeRates($baseCurrency);

            // Cache the fresh data
            $this->cache->store($baseCurrency, $rates);

            return [
                'rates' => $rates,
                'from_cache' => false,
            ];
        } catch (CurrencyConverterException $e) {
            // If API fails, try to use expired cache as fallback
            $expiredRates = $this->getExpiredCacheRates($baseCurrency);
            if ($expiredRates !== null) {
                error_log("Using expired cache due to API failure: " . $e->getMessage());
                return [
                    'rates' => $expiredRates,
                    'from_cache' => true,
                ];
            }

            throw $e;
        }
    }

    /**
     * Get expired cache rates as fallback
     *
     * @param SupportedCurrency $baseCurrency Base currency
     * @return array<string, float>|null Expired rates or null if not available
     */
    private function getExpiredCacheRates(SupportedCurrency $baseCurrency): ?array
    {
        if (!file_exists(AppConfig::CACHE_FILE)) {
            return null;
        }

        try {
            $cacheContent = file_get_contents(AppConfig::CACHE_FILE);
            if ($cacheContent === false) {
                return null;
            }

            $cacheData = json_decode($cacheContent, true, 512, JSON_THROW_ON_ERROR);

            if (is_array($cacheData) &&
                isset($cacheData['base'], $cacheData['rates']) &&
                $cacheData['base'] === $baseCurrency->value &&
                is_array($cacheData['rates'])) {
                return $cacheData['rates'];
            }
        } catch (JsonException) {
            // Ignore JSON errors for expired cache
        }

        return null;
    }

    /**
     * Validate currency code and return enum instance
     *
     * @param string $currencyCode Currency code to validate
     * @return SupportedCurrency Validated currency enum
     * @throws CurrencyConverterException If currency is not supported
     */
    private function validateCurrency(string $currencyCode): SupportedCurrency
    {
        $currency = SupportedCurrency::tryFrom(strtoupper(trim($currencyCode)));

        if ($currency === null) {
            throw new CurrencyConverterException(
                ErrorMessage::INVALID_CURRENCY->value,
                context: [
                    'provided_currency' => $currencyCode,
                    'supported_currencies' => array_column(SupportedCurrency::cases(), 'value'),
                ]
            );
        }

        return $currency;
    }

    /**
     * Validate conversion amount
     *
     * @param float $amount Amount to validate
     * @throws CurrencyConverterException If amount is invalid
     */
    private function validateAmount(float $amount): void
    {
        if (!is_finite($amount) || $amount < AppConfig::MIN_AMOUNT || $amount > AppConfig::MAX_AMOUNT) {
            throw new CurrencyConverterException(
                ErrorMessage::INVALID_AMOUNT->format(
                    AppConfig::MIN_AMOUNT,
                    AppConfig::MAX_AMOUNT
                ),
                context: [
                    'provided_amount' => $amount,
                    'min_amount' => AppConfig::MIN_AMOUNT,
                    'max_amount' => AppConfig::MAX_AMOUNT,
                ]
            );
        }
    }
}
