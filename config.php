<?php

declare(strict_types=1);

/**
 * Currency Converter Application Configuration
 *
 * This file contains all configuration constants, supported currencies,
 * error messages, and application settings for the Currency Converter.
 *
 * PHP Version 7.4+ (Compatible with 8.4+)
 *
 * @category  Configuration
 * @package   CurrencyConverter
 * <AUTHOR> Converter Team
 * @license   MIT License
 * @version   2.0.0
 * @since     1.0.0
 */

/**
 * Application Configuration Class
 *
 * Centralized configuration management with expert-level code quality
 * and PHP 7.4+ compatibility.
 */
final class AppConfig
{
    /**
     * Exchange Rate API Configuration
     */
    public const EXCHANGE_API_URL = 'https://api.exchangerate-api.com/v4/latest/';
    public const FALLBACK_API_URL = 'https://api.fixer.io/latest';

    /**
     * Cache Configuration
     */
    public const CACHE_DURATION = 3600; // 1 hour in seconds
    public const CACHE_DIRECTORY = 'cache';
    public const CACHE_FILE = self::CACHE_DIRECTORY . '/exchange_rates.json';
    public const CACHE_FILE_PERMISSIONS = 0644;
    public const CACHE_DIR_PERMISSIONS = 0755;

    /**
     * Application Defaults
     */
    public const DEFAULT_FROM_CURRENCY = 'USD';
    public const DEFAULT_TO_CURRENCY = 'EUR';
    public const DEFAULT_AMOUNT = 1.0;

    /**
     * API Request Configuration
     */
    public const API_TIMEOUT = 10;
    public const USER_AGENT = 'Currency Converter App v2.0';
    public const MAX_RETRIES = 3;
    public const RETRY_DELAY = 1; // seconds

    /**
     * Validation Constraints
     */
    public const MIN_AMOUNT = 0.01;
    public const MAX_AMOUNT = 999999999.99;
    public const DECIMAL_PRECISION = 2;

    /**
     * Security Configuration
     */
    public const MAX_REQUEST_SIZE = 1024; // bytes
    public const ALLOWED_METHODS = ['GET', 'POST'];
    public const CSRF_TOKEN_NAME = '_token';
}

/**
 * Currency Management Class
 *
 * Defines all supported currencies with their ISO codes and full names.
 * PHP 7.4+ compatible implementation with type safety and expert documentation.
 */
final class SupportedCurrency
{
    // Currency constants
    public const USD = 'USD'; // US Dollar
    public const EUR = 'EUR'; // Euro
    public const GBP = 'GBP'; // British Pound Sterling
    public const JPY = 'JPY'; // Japanese Yen
    public const AUD = 'AUD'; // Australian Dollar
    public const CAD = 'CAD'; // Canadian Dollar
    public const CHF = 'CHF'; // Swiss Franc
    public const CNY = 'CNY'; // Chinese Yuan Renminbi
    public const SEK = 'SEK'; // Swedish Krona
    public const NZD = 'NZD'; // New Zealand Dollar
    public const MXN = 'MXN'; // Mexican Peso
    public const SGD = 'SGD'; // Singapore Dollar
    public const HKD = 'HKD'; // Hong Kong Dollar
    public const NOK = 'NOK'; // Norwegian Krone
    public const TRY = 'TRY'; // Turkish Lira
    public const RUB = 'RUB'; // Russian Ruble
    public const INR = 'INR'; // Indian Rupee
    public const BRL = 'BRL'; // Brazilian Real
    public const ZAR = 'ZAR'; // South African Rand
    public const KRW = 'KRW'; // South Korean Won
    public const PLN = 'PLN'; // Polish Zloty
    public const CZK = 'CZK'; // Czech Koruna
    public const HUF = 'HUF'; // Hungarian Forint
    public const RON = 'RON'; // Romanian Leu
    public const BGN = 'BGN'; // Bulgarian Lev
    public const HRK = 'HRK'; // Croatian Kuna
    public const DKK = 'DKK'; // Danish Krone
    public const ISK = 'ISK'; // Icelandic Krona
    public const THB = 'THB'; // Thai Baht
    public const MYR = 'MYR'; // Malaysian Ringgit
    public const PHP = 'PHP'; // Philippine Peso
    public const IDR = 'IDR'; // Indonesian Rupiah
    public const VND = 'VND'; // Vietnamese Dong
    public const AED = 'AED'; // UAE Dirham
    public const SAR = 'SAR'; // Saudi Riyal
    public const QAR = 'QAR'; // Qatari Riyal
    public const KWD = 'KWD'; // Kuwaiti Dinar
    public const BHD = 'BHD'; // Bahraini Dinar
    public const OMR = 'OMR'; // Omani Rial
    public const JOD = 'JOD'; // Jordanian Dinar
    public const LBP = 'LBP'; // Lebanese Pound
    public const EGP = 'EGP'; // Egyptian Pound
    public const ILS = 'ILS'; // Israeli New Shekel
    public const CLP = 'CLP'; // Chilean Peso
    public const COP = 'COP'; // Colombian Peso
    public const PEN = 'PEN'; // Peruvian Sol
    public const UYU = 'UYU'; // Uruguayan Peso
    public const ARS = 'ARS'; // Argentine Peso

    /**
     * Currency names mapping
     *
     * @var array<string, string>
     */
    private static $currencyNames = [
        self::USD => 'US Dollar',
        self::EUR => 'Euro',
        self::GBP => 'British Pound Sterling',
        self::JPY => 'Japanese Yen',
        self::AUD => 'Australian Dollar',
        self::CAD => 'Canadian Dollar',
        self::CHF => 'Swiss Franc',
        self::CNY => 'Chinese Yuan Renminbi',
        self::SEK => 'Swedish Krona',
        self::NZD => 'New Zealand Dollar',
        self::MXN => 'Mexican Peso',
        self::SGD => 'Singapore Dollar',
        self::HKD => 'Hong Kong Dollar',
        self::NOK => 'Norwegian Krone',
        self::TRY => 'Turkish Lira',
        self::RUB => 'Russian Ruble',
        self::INR => 'Indian Rupee',
        self::BRL => 'Brazilian Real',
        self::ZAR => 'South African Rand',
        self::KRW => 'South Korean Won',
        self::PLN => 'Polish Zloty',
        self::CZK => 'Czech Koruna',
        self::HUF => 'Hungarian Forint',
        self::RON => 'Romanian Leu',
        self::BGN => 'Bulgarian Lev',
        self::HRK => 'Croatian Kuna',
        self::DKK => 'Danish Krone',
        self::ISK => 'Icelandic Krona',
        self::THB => 'Thai Baht',
        self::MYR => 'Malaysian Ringgit',
        self::PHP => 'Philippine Peso',
        self::IDR => 'Indonesian Rupiah',
        self::VND => 'Vietnamese Dong',
        self::AED => 'UAE Dirham',
        self::SAR => 'Saudi Riyal',
        self::QAR => 'Qatari Riyal',
        self::KWD => 'Kuwaiti Dinar',
        self::BHD => 'Bahraini Dinar',
        self::OMR => 'Omani Rial',
        self::JOD => 'Jordanian Dinar',
        self::LBP => 'Lebanese Pound',
        self::EGP => 'Egyptian Pound',
        self::ILS => 'Israeli New Shekel',
        self::CLP => 'Chilean Peso',
        self::COP => 'Colombian Peso',
        self::PEN => 'Peruvian Sol',
        self::UYU => 'Uruguayan Peso',
        self::ARS => 'Argentine Peso',
    ];

    /**
     * Currency symbols mapping
     *
     * @var array<string, string>
     */
    private static $currencySymbols = [
        self::USD => '$',
        self::EUR => '€',
        self::GBP => '£',
        self::JPY => '¥',
        self::CNY => '¥',
        self::INR => '₹',
        self::KRW => '₩',
        self::RUB => '₽',
    ];

    /**
     * Get the full name of the currency
     *
     * @param string $currencyCode Currency code
     * @return string The full currency name
     */
    public static function getFullName(string $currencyCode): string
    {
        return self::$currencyNames[$currencyCode] ?? $currencyCode;
    }

    /**
     * Get the currency symbol
     *
     * @param string $currencyCode Currency code
     * @return string The currency symbol or code if no symbol available
     */
    public static function getSymbol(string $currencyCode): string
    {
        return self::$currencySymbols[$currencyCode] ?? $currencyCode . ' ';
    }

    /**
     * Get all supported currencies as an associative array
     *
     * @return array<string, string> Array of currency codes and names
     */
    public static function getAllCurrencies(): array
    {
        return self::$currencyNames;
    }

    /**
     * Check if a currency code is supported
     *
     * @param string $code The currency code to check
     * @return bool True if supported, false otherwise
     */
    public static function isSupported(string $code): bool
    {
        return isset(self::$currencyNames[$code]);
    }

    /**
     * Get all currency codes
     *
     * @return array<string> Array of currency codes
     */
    public static function getAllCodes(): array
    {
        return array_keys(self::$currencyNames);
    }
}

/**
 * Error Message Management Class
 *
 * Centralized error message management with type safety.
 * PHP 7.4+ compatible implementation.
 */
final class ErrorMessage
{
    public const API_ERROR = 'Unable to fetch exchange rates. Please try again later.';
    public const INVALID_CURRENCY = 'Invalid currency code provided.';
    public const INVALID_AMOUNT = 'Please enter a valid amount between %s and %s.';
    public const CACHE_ERROR = 'Unable to cache exchange rates.';
    public const NETWORK_ERROR = 'Network error occurred. Please check your internet connection.';
    public const RATE_LIMIT_EXCEEDED = 'API rate limit exceeded. Please try again later.';
    public const INVALID_REQUEST = 'Invalid request format.';
    public const SECURITY_ERROR = 'Security validation failed.';
    public const FILE_PERMISSION_ERROR = 'File permission error. Please check directory permissions.';

    /**
     * Get formatted error message with parameters
     *
     * @param string $message Error message template
     * @param mixed ...$params Parameters to format into the message
     * @return string Formatted error message
     */
    public static function format(string $message, ...$params): string
    {
        return sprintf($message, ...$params);
    }
}

/**
 * Initialize application environment
 *
 * Creates necessary directories and sets up the environment
 * for the currency converter application.
 *
 * @throws RuntimeException If directory creation fails
 */
function initializeEnvironment(): void
{
    $cacheDir = AppConfig::CACHE_DIRECTORY;

    if (!is_dir($cacheDir)) {
        if (!mkdir($cacheDir, AppConfig::CACHE_DIR_PERMISSIONS, true)) {
            throw new RuntimeException(ErrorMessage::FILE_PERMISSION_ERROR);
        }
    }

    // Ensure cache directory is writable
    if (!is_writable($cacheDir)) {
        throw new RuntimeException(ErrorMessage::FILE_PERMISSION_ERROR);
    }
}

// Initialize the environment
try {
    initializeEnvironment();
} catch (RuntimeException $e) {
    error_log("Currency Converter initialization error: " . $e->getMessage());
    // Continue execution but log the error
}
